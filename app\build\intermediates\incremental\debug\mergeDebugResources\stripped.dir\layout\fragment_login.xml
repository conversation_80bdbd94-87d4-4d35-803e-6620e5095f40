<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Username Field -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Tên đăng nhập"
        app:startIconDrawable="@drawable/ic_person"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_username"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:imeOptions="actionNext" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Password Field -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:hint="Mật khẩu"
        app:startIconDrawable="@drawable/ic_lock"
        app:endIconMode="password_toggle"
        app:boxStrokeColor="@color/primary"
        app:hintTextColor="@color/primary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPassword"
            android:maxLines="1"
            android:imeOptions="actionDone" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Remember Me -->
    <CheckBox
        android:id="@+id/cb_remember_me"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Ghi nhớ đăng nhập"
        android:textSize="14sp"
        android:layout_marginBottom="24dp"
        android:buttonTint="@color/primary" />

    <!-- Login Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_login"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="Đăng nhập"
        android:textSize="16sp"
        android:textStyle="bold"
        android:backgroundTint="@color/primary"
        android:textColor="@android:color/white"
        app:cornerRadius="12dp"
        android:layout_marginBottom="16dp" />

    <!-- Forgot Password -->
    <TextView
        android:id="@+id/tv_forgot_password"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="Quên mật khẩu?"
        android:textSize="14sp"
        android:textColor="@color/primary"
        android:padding="8dp"
        android:background="?attr/selectableItemBackgroundBorderless" />

    <!-- Loading Progress -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        android:indeterminateTint="@color/primary" />

</LinearLayout>
