<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_task_detail" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\dialog_task_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/dialog_task_detail_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="148" endOffset="12"/></Target><Target id="@+id/tv_task_title" view="TextView"><Expressions/><location startLine="14" startOffset="8" endLine="22" endOffset="47"/></Target><Target id="@+id/tv_task_description" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="33" endOffset="39"/></Target><Target id="@+id/chip_category" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="43" startOffset="12" endLine="50" endOffset="43"/></Target><Target id="@+id/chip_priority" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="53" startOffset="12" endLine="59" endOffset="43"/></Target><Target id="@+id/layout_progress" view="LinearLayout"><Expressions/><location startLine="64" startOffset="8" endLine="95" endOffset="22"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="79" startOffset="12" endLine="85" endOffset="55"/></Target><Target id="@+id/tv_progress_text" view="TextView"><Expressions/><location startLine="87" startOffset="12" endLine="93" endOffset="50"/></Target><Target id="@+id/btn_add_step_detail" view="Button"><Expressions/><location startLine="113" startOffset="12" endLine="121" endOffset="56"/></Target><Target id="@+id/recycler_view_steps_detail" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="126" startOffset="8" endLine="132" endOffset="52"/></Target><Target id="@+id/tv_no_steps" view="TextView"><Expressions/><location startLine="135" startOffset="8" endLine="144" endOffset="39"/></Target></Targets></Layout>