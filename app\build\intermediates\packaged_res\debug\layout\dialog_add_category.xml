<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp">

    <!-- Category Name -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Tên danh mục"
        android:layout_marginBottom="16dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_category_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:imeOptions="actionNext" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Icon Selection -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="<PERSON><PERSON>n biểu tượng"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:columnCount="6"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btn_icon_work"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="💼"
            android:textSize="20sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_icon_personal"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="👤"
            android:textSize="20sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_icon_study"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="📚"
            android:textSize="20sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_icon_health"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="💪"
            android:textSize="20sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_icon_shopping"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="🛒"
            android:textSize="20sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_icon_family"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="👨‍👩‍👧‍👦"
            android:textSize="16sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_icon_travel"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="✈️"
            android:textSize="20sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_icon_food"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="🍽️"
            android:textSize="20sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_icon_hobby"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="🎨"
            android:textSize="20sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_icon_finance"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="💰"
            android:textSize="20sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_icon_home"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="🏠"
            android:textSize="20sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btn_icon_other"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="📝"
            android:textSize="20sp"
            android:layout_margin="4dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </GridLayout>

    <!-- Color Selection -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Chọn màu sắc"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:columnCount="6">

        <View
            android:id="@+id/color_blue"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="#2196F3"
            android:layout_margin="4dp"
            android:clickable="true"
            android:focusable="true" />

        <View
            android:id="@+id/color_green"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="#4CAF50"
            android:layout_margin="4dp"
            android:clickable="true"
            android:focusable="true" />

        <View
            android:id="@+id/color_orange"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="#FF9800"
            android:layout_margin="4dp"
            android:clickable="true"
            android:focusable="true" />

        <View
            android:id="@+id/color_red"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="#F44336"
            android:layout_margin="4dp"
            android:clickable="true"
            android:focusable="true" />

        <View
            android:id="@+id/color_purple"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="#9C27B0"
            android:layout_margin="4dp"
            android:clickable="true"
            android:focusable="true" />

        <View
            android:id="@+id/color_pink"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="#E91E63"
            android:layout_margin="4dp"
            android:clickable="true"
            android:focusable="true" />

    </GridLayout>

</LinearLayout>
