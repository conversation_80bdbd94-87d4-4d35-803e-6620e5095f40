<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp">

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Tiêu đề nhiệm vụ">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_task_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="<PERSON><PERSON> tả">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_task_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minLines="3" />

    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Danh mục"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <Spinner
        android:id="@+id/spinner_category"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Mức độ ưu tiên"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <Spinner
        android:id="@+id/spinner_priority"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp" />

    <!-- Start Date Section -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Thời gian bắt đầu"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:id="@+id/tv_selected_start_date"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Chọn thời gian bắt đầu"
            android:gravity="center_vertical"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:background="@drawable/bg_input_field"
            android:drawableEnd="@drawable/ic_calendar"
            android:drawablePadding="8dp" />

        <Button
            android:id="@+id/btn_select_start_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Chọn"
            android:layout_marginStart="8dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </LinearLayout>

    <!-- Due Date Section -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Thời gian đến hạn"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:id="@+id/tv_selected_date"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Chọn ngày đến hạn"
            android:gravity="center_vertical"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:background="@drawable/bg_input_field"
            android:drawableEnd="@drawable/ic_calendar"
            android:drawablePadding="8dp" />

        <Button
            android:id="@+id/btn_select_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Chọn ngày"
            android:layout_marginStart="8dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </LinearLayout>

    <!-- Steps Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Các bước thực hiện"
            android:textSize="16sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_add_step"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="+ Thêm bước"
            android:textSize="12sp"
            android:backgroundTint="@color/primary"
            android:textColor="@android:color/white"
            style="@style/Widget.Material3.Button" />

    </LinearLayout>

    <!-- Steps RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_steps"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:maxHeight="200dp"
        android:nestedScrollingEnabled="false" />

</LinearLayout>
