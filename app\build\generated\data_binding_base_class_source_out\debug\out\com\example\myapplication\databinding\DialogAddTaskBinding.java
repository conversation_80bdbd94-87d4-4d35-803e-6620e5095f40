// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddTaskBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnAddStep;

  @NonNull
  public final Button btnSelectDate;

  @NonNull
  public final Button btnSelectStartDate;

  @NonNull
  public final TextInputEditText etTaskDescription;

  @NonNull
  public final TextInputEditText etTaskTitle;

  @NonNull
  public final RecyclerView recyclerViewSteps;

  @NonNull
  public final Spinner spinnerCategory;

  @NonNull
  public final Spinner spinnerPriority;

  @NonNull
  public final TextView tvSelectedDate;

  @NonNull
  public final TextView tvSelectedStartDate;

  private DialogAddTaskBinding(@NonNull LinearLayout rootView, @NonNull Button btnAddStep,
      @NonNull Button btnSelectDate, @NonNull Button btnSelectStartDate,
      @NonNull TextInputEditText etTaskDescription, @NonNull TextInputEditText etTaskTitle,
      @NonNull RecyclerView recyclerViewSteps, @NonNull Spinner spinnerCategory,
      @NonNull Spinner spinnerPriority, @NonNull TextView tvSelectedDate,
      @NonNull TextView tvSelectedStartDate) {
    this.rootView = rootView;
    this.btnAddStep = btnAddStep;
    this.btnSelectDate = btnSelectDate;
    this.btnSelectStartDate = btnSelectStartDate;
    this.etTaskDescription = etTaskDescription;
    this.etTaskTitle = etTaskTitle;
    this.recyclerViewSteps = recyclerViewSteps;
    this.spinnerCategory = spinnerCategory;
    this.spinnerPriority = spinnerPriority;
    this.tvSelectedDate = tvSelectedDate;
    this.tvSelectedStartDate = tvSelectedStartDate;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_add_step;
      Button btnAddStep = ViewBindings.findChildViewById(rootView, id);
      if (btnAddStep == null) {
        break missingId;
      }

      id = R.id.btn_select_date;
      Button btnSelectDate = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectDate == null) {
        break missingId;
      }

      id = R.id.btn_select_start_date;
      Button btnSelectStartDate = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectStartDate == null) {
        break missingId;
      }

      id = R.id.et_task_description;
      TextInputEditText etTaskDescription = ViewBindings.findChildViewById(rootView, id);
      if (etTaskDescription == null) {
        break missingId;
      }

      id = R.id.et_task_title;
      TextInputEditText etTaskTitle = ViewBindings.findChildViewById(rootView, id);
      if (etTaskTitle == null) {
        break missingId;
      }

      id = R.id.recycler_view_steps;
      RecyclerView recyclerViewSteps = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewSteps == null) {
        break missingId;
      }

      id = R.id.spinner_category;
      Spinner spinnerCategory = ViewBindings.findChildViewById(rootView, id);
      if (spinnerCategory == null) {
        break missingId;
      }

      id = R.id.spinner_priority;
      Spinner spinnerPriority = ViewBindings.findChildViewById(rootView, id);
      if (spinnerPriority == null) {
        break missingId;
      }

      id = R.id.tv_selected_date;
      TextView tvSelectedDate = ViewBindings.findChildViewById(rootView, id);
      if (tvSelectedDate == null) {
        break missingId;
      }

      id = R.id.tv_selected_start_date;
      TextView tvSelectedStartDate = ViewBindings.findChildViewById(rootView, id);
      if (tvSelectedStartDate == null) {
        break missingId;
      }

      return new DialogAddTaskBinding((LinearLayout) rootView, btnAddStep, btnSelectDate,
          btnSelectStartDate, etTaskDescription, etTaskTitle, recyclerViewSteps, spinnerCategory,
          spinnerPriority, tvSelectedDate, tvSelectedStartDate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
