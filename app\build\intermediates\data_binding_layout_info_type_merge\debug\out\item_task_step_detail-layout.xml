<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_task_step_detail" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\item_task_step_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_task_step_detail_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="112" endOffset="51"/></Target><Target id="@+id/cb_step_completed" view="CheckBox"><Expressions/><location startLine="19" startOffset="8" endLine="24" endOffset="49"/></Target><Target id="@+id/tv_step_title" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="43" endOffset="41"/></Target><Target id="@+id/tv_step_description" view="TextView"><Expressions/><location startLine="46" startOffset="12" endLine="56" endOffset="43"/></Target><Target id="@+id/tv_step_status" view="TextView"><Expressions/><location startLine="66" startOffset="16" endLine="75" endOffset="51"/></Target><Target id="@+id/btn_edit_step" view="ImageButton"><Expressions/><location startLine="88" startOffset="12" endLine="96" endOffset="47"/></Target><Target id="@+id/btn_delete_step_detail" view="ImageButton"><Expressions/><location startLine="99" startOffset="12" endLine="106" endOffset="45"/></Target></Targets></Layout>