<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_category" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\dialog_add_category.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_add_category_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="216" endOffset="14"/></Target><Target id="@+id/et_category_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="45"/></Target><Target id="@+id/btn_icon_work" view="Button"><Expressions/><location startLine="37" startOffset="8" endLine="44" endOffset="67"/></Target><Target id="@+id/btn_icon_personal" view="Button"><Expressions/><location startLine="46" startOffset="8" endLine="53" endOffset="67"/></Target><Target id="@+id/btn_icon_study" view="Button"><Expressions/><location startLine="55" startOffset="8" endLine="62" endOffset="67"/></Target><Target id="@+id/btn_icon_health" view="Button"><Expressions/><location startLine="64" startOffset="8" endLine="71" endOffset="67"/></Target><Target id="@+id/btn_icon_shopping" view="Button"><Expressions/><location startLine="73" startOffset="8" endLine="80" endOffset="67"/></Target><Target id="@+id/btn_icon_family" view="Button"><Expressions/><location startLine="82" startOffset="8" endLine="89" endOffset="67"/></Target><Target id="@+id/btn_icon_travel" view="Button"><Expressions/><location startLine="91" startOffset="8" endLine="98" endOffset="67"/></Target><Target id="@+id/btn_icon_food" view="Button"><Expressions/><location startLine="100" startOffset="8" endLine="107" endOffset="67"/></Target><Target id="@+id/btn_icon_hobby" view="Button"><Expressions/><location startLine="109" startOffset="8" endLine="116" endOffset="67"/></Target><Target id="@+id/btn_icon_finance" view="Button"><Expressions/><location startLine="118" startOffset="8" endLine="125" endOffset="67"/></Target><Target id="@+id/btn_icon_home" view="Button"><Expressions/><location startLine="127" startOffset="8" endLine="134" endOffset="67"/></Target><Target id="@+id/btn_icon_other" view="Button"><Expressions/><location startLine="136" startOffset="8" endLine="143" endOffset="67"/></Target><Target id="@+id/color_blue" view="View"><Expressions/><location startLine="160" startOffset="8" endLine="167" endOffset="38"/></Target><Target id="@+id/color_green" view="View"><Expressions/><location startLine="169" startOffset="8" endLine="176" endOffset="38"/></Target><Target id="@+id/color_orange" view="View"><Expressions/><location startLine="178" startOffset="8" endLine="185" endOffset="38"/></Target><Target id="@+id/color_red" view="View"><Expressions/><location startLine="187" startOffset="8" endLine="194" endOffset="38"/></Target><Target id="@+id/color_purple" view="View"><Expressions/><location startLine="196" startOffset="8" endLine="203" endOffset="38"/></Target><Target id="@+id/color_pink" view="View"><Expressions/><location startLine="205" startOffset="8" endLine="212" endOffset="38"/></Target></Targets></Layout>