1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
14-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:8:5-79
14-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:8:22-76
15    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
15-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:9:5-74
15-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:9:22-71
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:10:5-68
16-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
17-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:11:5-81
17-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:11:22-78
18
19    <permission
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:13:5-59:19
26        android:allowBackup="true"
26-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:14:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:15:9-65
29        android:debuggable="true"
30        android:fullBackupContent="@xml/backup_rules"
30-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:16:9-54
31        android:icon="@mipmap/ic_launcher"
31-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:17:9-43
32        android:label="@string/app_name"
32-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:18:9-41
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:19:9-54
34        android:supportsRtl="true"
34-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:20:9-35
35        android:testOnly="true"
36        android:theme="@style/Theme.MyApplication" >
36-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:21:9-51
37        <activity
37-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:23:9-37:20
38            android:name="com.example.myapplication.MainActivity"
38-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:24:13-41
39            android:exported="true"
39-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:25:13-36
40            android:label="@string/app_name"
40-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:26:13-45
41            android:theme="@style/Theme.MyApplication.NoActionBar" >
41-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:27:13-67
42            <intent-filter>
42-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:28:13-32:29
43                <action android:name="android.intent.action.MAIN" />
43-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:29:17-69
43-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:29:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:31:17-77
45-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:31:27-74
46            </intent-filter>
47
48            <meta-data
48-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:34:13-36:36
49                android:name="android.app.lib_name"
49-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:35:17-52
50                android:value="" />
50-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:36:17-33
51        </activity>
52        <activity
52-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:39:9-42:70
53            android:name="com.example.myapplication.activities.AuthActivity"
53-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:40:13-52
54            android:exported="false"
54-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:41:13-37
55            android:theme="@style/Theme.MyApplication.NoActionBar" />
55-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:42:13-67
56        <activity
56-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:44:9-53:20
57            android:name="com.example.myapplication.TaskDetailActivity"
57-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:45:13-47
58            android:exported="false"
58-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:46:13-37
59            android:label="Chi tiết Task"
59-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:47:13-42
60            android:parentActivityName="com.example.myapplication.MainActivity"
60-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:48:13-55
61            android:theme="@style/Theme.MyApplication.NoActionBar" >
61-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:49:13-67
62            <meta-data
62-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:50:13-52:49
63                android:name="android.support.PARENT_ACTIVITY"
63-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:51:17-63
64                android:value=".MainActivity" />
64-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:52:17-46
65        </activity>
66
67        <receiver
67-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:55:9-58:40
68            android:name="com.example.myapplication.receivers.TaskNotificationReceiver"
68-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:56:13-63
69            android:enabled="true"
69-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:57:13-35
70            android:exported="false" />
70-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:58:13-37
71
72        <service
72-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
73            android:name="androidx.room.MultiInstanceInvalidationService"
73-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
74            android:directBootAware="true"
74-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
75            android:exported="false" />
75-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
76
77        <provider
77-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
78            android:name="androidx.startup.InitializationProvider"
78-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
79            android:authorities="com.example.myapplication.androidx-startup"
79-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
80            android:exported="false" >
80-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
81            <meta-data
81-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
82                android:name="androidx.emoji2.text.EmojiCompatInitializer"
82-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
83                android:value="androidx.startup" />
83-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
84            <meta-data
84-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
85                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
85-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
86                android:value="androidx.startup" />
86-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
87            <meta-data
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
88                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
89                android:value="androidx.startup" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
90        </provider>
91
92        <uses-library
92-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
93            android:name="androidx.window.extensions"
93-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
94            android:required="false" />
94-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
95        <uses-library
95-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
96            android:name="androidx.window.sidecar"
96-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
97            android:required="false" />
97-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
98
99        <receiver
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
100            android:name="androidx.profileinstaller.ProfileInstallReceiver"
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
101            android:directBootAware="false"
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
102            android:enabled="true"
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
103            android:exported="true"
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
104            android:permission="android.permission.DUMP" >
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
106                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
109                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
110            </intent-filter>
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
112                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
113            </intent-filter>
114            <intent-filter>
114-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
115                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
116            </intent-filter>
117        </receiver>
118    </application>
119
120</manifest>
