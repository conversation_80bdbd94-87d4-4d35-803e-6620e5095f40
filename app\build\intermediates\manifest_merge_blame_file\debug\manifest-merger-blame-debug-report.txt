1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
14-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:8:5-79
14-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:8:22-76
15    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
15-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:9:5-74
15-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:9:22-71
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:10:5-68
16-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
17-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:11:5-81
17-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:11:22-78
18
19    <permission
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:13:5-64:19
26        android:allowBackup="true"
26-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:14:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\8f355e2c491578f6678abe77c9acfefb\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:15:9-65
29        android:debuggable="true"
30        android:fullBackupContent="@xml/backup_rules"
30-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:16:9-54
31        android:icon="@mipmap/ic_launcher"
31-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:17:9-43
32        android:label="@string/app_name"
32-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:18:9-41
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:19:9-54
34        android:supportsRtl="true"
34-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:20:9-35
35        android:theme="@style/Theme.MyApplication" >
35-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:21:9-51
36        <activity
36-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:23:9-37:20
37            android:name="com.example.myapplication.MainActivity"
37-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:24:13-41
38            android:exported="true"
38-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:25:13-36
39            android:label="@string/app_name"
39-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:26:13-45
40            android:theme="@style/Theme.MyApplication.NoActionBar" >
40-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:27:13-67
41            <intent-filter>
41-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:28:13-32:29
42                <action android:name="android.intent.action.MAIN" />
42-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:29:17-69
42-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:29:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:31:17-77
44-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:31:27-74
45            </intent-filter>
46
47            <meta-data
47-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:34:13-36:36
48                android:name="android.app.lib_name"
48-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:35:17-52
49                android:value="" />
49-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:36:17-33
50        </activity>
51        <activity
51-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:39:9-42:70
52            android:name="com.example.myapplication.activities.AuthActivity"
52-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:40:13-52
53            android:exported="false"
53-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:41:13-37
54            android:theme="@style/Theme.MyApplication.NoActionBar" />
54-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:42:13-67
55        <activity
55-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:44:9-53:20
56            android:name="com.example.myapplication.TaskDetailActivity"
56-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:45:13-47
57            android:exported="false"
57-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:46:13-37
58            android:label="Chi tiết Task"
58-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:47:13-42
59            android:parentActivityName="com.example.myapplication.MainActivity"
59-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:48:13-55
60            android:theme="@style/Theme.MyApplication.NoActionBar" >
60-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:49:13-67
61            <meta-data
61-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:50:13-52:49
62                android:name="android.support.PARENT_ACTIVITY"
62-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:51:17-63
63                android:value=".MainActivity" />
63-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:52:17-46
64        </activity>
65
66        <receiver
66-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:55:9-58:40
67            android:name="com.example.myapplication.receivers.TaskNotificationReceiver"
67-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:56:13-63
68            android:enabled="true"
68-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:57:13-35
69            android:exported="false" />
69-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:58:13-37
70
71        <service
71-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:60:9-63:40
72            android:name="com.example.myapplication.services.TaskNotificationService"
72-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:61:13-61
73            android:enabled="true"
73-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:62:13-35
74            android:exported="false" />
74-->D:\abc\DuyVC\app\src\main\AndroidManifest.xml:63:13-37
75        <service
75-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
76            android:name="androidx.room.MultiInstanceInvalidationService"
76-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
77            android:directBootAware="true"
77-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
78            android:exported="false" />
78-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\98d3414a47e6560f903b3c92ea58399b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
79
80        <provider
80-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
81            android:name="androidx.startup.InitializationProvider"
81-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
82            android:authorities="com.example.myapplication.androidx-startup"
82-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
83            android:exported="false" >
83-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
84            <meta-data
84-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
85                android:name="androidx.emoji2.text.EmojiCompatInitializer"
85-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
86                android:value="androidx.startup" />
86-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b24562ad32a12353c36bb387650ca04c\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
87            <meta-data
87-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
88                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
88-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
89                android:value="androidx.startup" />
89-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4d750175bb9f97e63db7264ecbd45b3b\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
90            <meta-data
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
91                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
92                android:value="androidx.startup" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
93        </provider>
94
95        <uses-library
95-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
96            android:name="androidx.window.extensions"
96-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
97            android:required="false" />
97-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
98        <uses-library
98-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
99            android:name="androidx.window.sidecar"
99-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
100            android:required="false" />
100-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e1bc3a0075959d1d61cffec1a5625715\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
101
102        <receiver
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
103            android:name="androidx.profileinstaller.ProfileInstallReceiver"
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
104            android:directBootAware="false"
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
105            android:enabled="true"
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
106            android:exported="true"
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
107            android:permission="android.permission.DUMP" >
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
109                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
110            </intent-filter>
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
112                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
113            </intent-filter>
114            <intent-filter>
114-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
115                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
116            </intent-filter>
117            <intent-filter>
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
118                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e01bbffe95cb281fd2110855be23ac9e\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
119            </intent-filter>
120        </receiver>
121    </application>
122
123</manifest>
