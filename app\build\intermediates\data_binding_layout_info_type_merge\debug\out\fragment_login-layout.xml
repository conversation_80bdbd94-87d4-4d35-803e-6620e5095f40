<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_login" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\fragment_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_login_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="94" endOffset="14"/></Target><Target id="@+id/et_username" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="18" startOffset="8" endLine="24" endOffset="45"/></Target><Target id="@+id/et_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="39" startOffset="8" endLine="45" endOffset="45"/></Target><Target id="@+id/cb_remember_me" view="CheckBox"><Expressions/><location startLine="50" startOffset="4" endLine="57" endOffset="45"/></Target><Target id="@+id/btn_login" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="60" startOffset="4" endLine="70" endOffset="44"/></Target><Target id="@+id/tv_forgot_password" view="TextView"><Expressions/><location startLine="73" startOffset="4" endLine="82" endOffset="71"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="85" startOffset="4" endLine="92" endOffset="52"/></Target></Targets></Layout>