[{"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/fragment_login.xml", "source": "com.example.myapplication.app-main-53:/layout/fragment_login.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/dialog_task_detail.xml", "source": "com.example.myapplication.app-main-53:/layout/dialog_task_detail.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/item_task_step_edit.xml", "source": "com.example.myapplication.app-main-53:/layout/item_task_step_edit.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/dialog_login.xml", "source": "com.example.myapplication.app-main-53:/layout/dialog_login.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/item_task_step_detail.xml", "source": "com.example.myapplication.app-main-53:/layout/item_task_step_detail.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/item_task.xml", "source": "com.example.myapplication.app-main-53:/layout/item_task.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/dialog_add_category.xml", "source": "com.example.myapplication.app-main-53:/layout/dialog_add_category.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/fragment_calendar.xml", "source": "com.example.myapplication.app-main-53:/layout/fragment_calendar.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/item_category.xml", "source": "com.example.myapplication.app-main-53:/layout/item_category.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/activity_main.xml", "source": "com.example.myapplication.app-main-53:/layout/activity_main.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/fragment_register.xml", "source": "com.example.myapplication.app-main-53:/layout/fragment_register.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/dialog_add_task.xml", "source": "com.example.myapplication.app-main-53:/layout/dialog_add_task.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/fragment_profile.xml", "source": "com.example.myapplication.app-main-53:/layout/fragment_profile.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/fragment_task_list.xml", "source": "com.example.myapplication.app-main-53:/layout/fragment_task_list.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/activity_auth.xml", "source": "com.example.myapplication.app-main-53:/layout/activity_auth.xml"}]