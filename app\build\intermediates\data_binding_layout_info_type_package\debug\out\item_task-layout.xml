<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_task" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\item_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_task_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="116" endOffset="51"/></Target><Target id="@+id/view_priority_indicator" view="View"><Expressions/><location startLine="17" startOffset="8" endLine="22" endOffset="42"/></Target><Target id="@+id/cb_task_completed" view="CheckBox"><Expressions/><location startLine="25" startOffset="8" endLine="31" endOffset="42"/></Target><Target id="@+id/tv_task_title" view="TextView"><Expressions/><location startLine="41" startOffset="12" endLine="49" endOffset="51"/></Target><Target id="@+id/tv_task_description" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="59" endOffset="51"/></Target><Target id="@+id/tv_task_category" view="TextView"><Expressions/><location startLine="68" startOffset="16" endLine="78" endOffset="71"/></Target><Target id="@+id/tv_task_priority" view="TextView"><Expressions/><location startLine="80" startOffset="16" endLine="89" endOffset="51"/></Target><Target id="@+id/tv_task_start_date" view="TextView"><Expressions/><location startLine="94" startOffset="12" endLine="101" endOffset="51"/></Target><Target id="@+id/tv_task_due_date" view="TextView"><Expressions/><location startLine="104" startOffset="12" endLine="110" endOffset="45"/></Target></Targets></Layout>