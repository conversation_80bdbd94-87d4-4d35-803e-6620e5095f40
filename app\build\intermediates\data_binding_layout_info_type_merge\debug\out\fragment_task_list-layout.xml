<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_task_list" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\fragment_task_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_task_list_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="172" endOffset="53"/></Target><Target id="@+id/et_search" view="EditText"><Expressions/><location startLine="51" startOffset="20" endLine="59" endOffset="49"/></Target><Target id="@+id/spinner_category_filter" view="Spinner"><Expressions/><location startLine="92" startOffset="16" endLine="96" endOffset="47"/></Target><Target id="@+id/btn_add_category" view="Button"><Expressions/><location startLine="98" startOffset="16" endLine="105" endOffset="75"/></Target><Target id="@+id/cb_show_incomplete_only" view="CheckBox"><Expressions/><location startLine="136" startOffset="16" endLine="144" endOffset="57"/></Target><Target id="@+id/recycler_view_tasks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="148" startOffset="12" endLine="155" endOffset="46"/></Target><Target id="@+id/fab_add_task" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="162" startOffset="4" endLine="170" endOffset="41"/></Target></Targets></Layout>