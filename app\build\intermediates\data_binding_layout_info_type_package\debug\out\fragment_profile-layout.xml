<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_profile" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\fragment_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_profile_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="348" endOffset="12"/></Target><Target id="@+id/layout_logged_in" view="LinearLayout"><Expressions/><location startLine="40" startOffset="8" endLine="298" endOffset="22"/></Target><Target id="@+id/iv_user_avatar" view="ImageView"><Expressions/><location startLine="67" startOffset="24" endLine="73" endOffset="55"/></Target><Target id="@+id/tv_user_initials" view="TextView"><Expressions/><location startLine="75" startOffset="24" endLine="84" endOffset="54"/></Target><Target id="@+id/tv_user_name" view="TextView"><Expressions/><location startLine="96" startOffset="24" endLine="103" endOffset="57"/></Target><Target id="@+id/tv_user_email" view="TextView"><Expressions/><location startLine="105" startOffset="24" endLine="112" endOffset="57"/></Target><Target id="@+id/menu_statistics" view="LinearLayout"><Expressions/><location startLine="128" startOffset="16" endLine="160" endOffset="30"/></Target><Target id="@+id/menu_settings" view="LinearLayout"><Expressions/><location startLine="169" startOffset="16" endLine="201" endOffset="30"/></Target><Target id="@+id/menu_help" view="LinearLayout"><Expressions/><location startLine="210" startOffset="16" endLine="242" endOffset="30"/></Target><Target id="@+id/menu_about" view="LinearLayout"><Expressions/><location startLine="251" startOffset="16" endLine="283" endOffset="30"/></Target><Target id="@+id/btn_logout" view="Button"><Expressions/><location startLine="286" startOffset="16" endLine="294" endOffset="45"/></Target><Target id="@+id/layout_not_logged_in" view="LinearLayout"><Expressions/><location startLine="301" startOffset="8" endLine="344" endOffset="22"/></Target><Target id="@+id/btn_login" view="Button"><Expressions/><location startLine="334" startOffset="12" endLine="342" endOffset="42"/></Target></Targets></Layout>