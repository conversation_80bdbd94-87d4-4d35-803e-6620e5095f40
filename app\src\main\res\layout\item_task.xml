<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    android:backgroundTint="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Priority Indicator -->
        <View
            android:id="@+id/view_priority_indicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="12dp"
            android:background="#FF9800" />

        <!-- Checkbox -->
        <CheckBox
            android:id="@+id/cb_task_completed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="12dp"
            android:buttonTint="#2196F3" />

        <!-- Task Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Title -->
            <TextView
                android:id="@+id/tv_task_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Task Title"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="4dp" />

            <!-- Description -->
            <TextView
                android:id="@+id/tv_task_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Task Description"
                android:textSize="14sp"
                android:textColor="#666666"
                android:layout_marginBottom="8dp" />

            <!-- Category and Priority Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/tv_task_category"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cá nhân"
                    android:textSize="12sp"
                    android:textColor="@android:color/white"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/rounded_background" />

                <TextView
                    android:id="@+id/tv_task_priority"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cao"
                    android:textSize="12sp"
                    android:textColor="#666666"
                    android:background="@drawable/priority_badge"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp" />

            </LinearLayout>

            <!-- Start Date -->
            <TextView
                android:id="@+id/tv_task_start_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🕐 2024-12-25 09:00"
                android:textSize="12sp"
                android:textColor="#4CAF50"
                android:layout_marginBottom="2dp" />

            <!-- Due Date -->
            <TextView
                android:id="@+id/tv_task_due_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="📅 2024-12-25 17:00"
                android:textSize="12sp"
                android:textColor="#666666" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
