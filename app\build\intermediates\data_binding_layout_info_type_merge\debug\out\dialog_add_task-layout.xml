<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_task" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\dialog_add_task.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_add_task_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="175" endOffset="14"/></Target><Target id="@+id/et_task_title" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="13" startOffset="8" endLine="16" endOffset="50"/></Target><Target id="@+id/et_task_description" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="26" startOffset="8" endLine="30" endOffset="34"/></Target><Target id="@+id/spinner_category" view="Spinner"><Expressions/><location startLine="42" startOffset="4" endLine="46" endOffset="44"/></Target><Target id="@+id/spinner_priority" view="Spinner"><Expressions/><location startLine="56" startOffset="4" endLine="60" endOffset="44"/></Target><Target id="@+id/tv_selected_start_date" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="88" endOffset="43"/></Target><Target id="@+id/btn_select_start_date" view="Button"><Expressions/><location startLine="90" startOffset="8" endLine="96" endOffset="67"/></Target><Target id="@+id/tv_selected_date" view="TextView"><Expressions/><location startLine="115" startOffset="8" endLine="126" endOffset="43"/></Target><Target id="@+id/btn_select_date" view="Button"><Expressions/><location startLine="128" startOffset="8" endLine="134" endOffset="67"/></Target><Target id="@+id/btn_add_step" view="Button"><Expressions/><location startLine="154" startOffset="8" endLine="162" endOffset="52"/></Target><Target id="@+id/recycler_view_steps" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="167" startOffset="4" endLine="173" endOffset="48"/></Target></Targets></Layout>