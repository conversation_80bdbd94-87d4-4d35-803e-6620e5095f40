package com.example.myapplication.utils;

import android.app.AlarmManager;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import com.example.myapplication.MainActivity;
import com.example.myapplication.R;
import com.example.myapplication.models.Task;
import com.example.myapplication.receivers.TaskNotificationReceiver;

import java.util.Calendar;
import java.util.Date;

public class NotificationHelper {
    private static final String CHANNEL_ID = "task_notifications";
    private static final String CHANNEL_NAME = "Task Notifications";
    private static final String CHANNEL_DESCRIPTION = "Notifications for upcoming tasks";
    private static final int NOTIFICATION_ID_BASE = 1000;

    private Context context;
    private NotificationManager notificationManager;
    private AlarmManager alarmManager;

    public NotificationHelper(Context context) {
        this.context = context;
        this.notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        this.alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        createNotificationChannel();
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            channel.enableVibration(true);
            channel.setVibrationPattern(new long[]{0, 1000, 500, 1000});
            notificationManager.createNotificationChannel(channel);
        }
    }

    public void scheduleTaskNotification(Task task) {
        if (task.getStartDate() == null || task.isCompleted()) {
            return;
        }

        Date notificationTime = task.getNotificationTime();
        if (notificationTime == null || notificationTime.before(new Date())) {
            return; // Don't schedule notifications for past times
        }

        Intent intent = new Intent(context, TaskNotificationReceiver.class);
        intent.putExtra("task_id", task.getId());
        intent.putExtra("task_title", task.getTitle());
        intent.putExtra("task_description", task.getDescription());
        intent.putExtra("start_time", task.getFormattedStartDate());

        int requestCode = getNotificationRequestCode(task.getId());
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                notificationTime.getTime(),
                pendingIntent
            );
        } else {
            alarmManager.setExact(
                AlarmManager.RTC_WAKEUP,
                notificationTime.getTime(),
                pendingIntent
            );
        }
    }

    public void cancelTaskNotification(String taskId) {
        Intent intent = new Intent(context, TaskNotificationReceiver.class);
        int requestCode = getNotificationRequestCode(taskId);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        alarmManager.cancel(pendingIntent);
    }

    public void showTaskNotification(String taskId, String title, String description, String startTime) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("Nhiệm vụ sắp bắt đầu!")
            .setContentText(title + " - Bắt đầu lúc " + startTime)
            .setStyle(new NotificationCompat.BigTextStyle()
                .bigText(description + "\n\nThời gian bắt đầu: " + startTime))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setVibrate(new long[]{0, 1000, 500, 1000})
            .setDefaults(NotificationCompat.DEFAULT_SOUND);

        int notificationId = getNotificationId(taskId);
        NotificationManagerCompat.from(context).notify(notificationId, builder.build());
    }

    private int getNotificationRequestCode(String taskId) {
        try {
            return NOTIFICATION_ID_BASE + Integer.parseInt(taskId);
        } catch (NumberFormatException e) {
            return NOTIFICATION_ID_BASE + taskId.hashCode();
        }
    }

    private int getNotificationId(String taskId) {
        try {
            return Integer.parseInt(taskId);
        } catch (NumberFormatException e) {
            return taskId.hashCode();
        }
    }

    public void rescheduleAllTaskNotifications(java.util.List<Task> tasks) {
        for (Task task : tasks) {
            if (!task.isCompleted() && task.getStartDate() != null) {
                scheduleTaskNotification(task);
            }
        }
    }
}
