<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeColor="#E0E0E0"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Drag Handle -->
        <ImageView
            android:id="@+id/iv_drag_handle"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_drag_handle"
            android:layout_marginEnd="8dp"
            android:contentDescription="Kéo để sắp xếp"
            android:alpha="0.6" />

        <!-- Step Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Step Title Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Tiêu đề bước"
                app:boxBackgroundMode="none"
                app:hintTextColor="@color/primary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_step_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:maxLines="1"
                    android:imeOptions="actionNext" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Step Description Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Mô tả (tùy chọn)"
                app:boxBackgroundMode="none"
                app:hintTextColor="@color/primary"
                android:layout_marginTop="4dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_step_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:maxLines="2"
                    android:imeOptions="actionDone" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Delete Button -->
        <ImageButton
            android:id="@+id/btn_delete_step"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_delete"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:layout_marginStart="8dp"
            android:contentDescription="Xóa bước"
            android:tint="@color/error" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
