<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="120dp"
    android:layout_height="100dp"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp"
        android:gravity="center">

        <TextView
            android:id="@+id/tv_category_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="👤"
            android:textSize="24sp"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tv_category_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cá nhân"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tv_task_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="5 nhiệm vụ"
            android:textSize="12sp"
            android:textColor="@android:color/white"
            android:alpha="0.8" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
