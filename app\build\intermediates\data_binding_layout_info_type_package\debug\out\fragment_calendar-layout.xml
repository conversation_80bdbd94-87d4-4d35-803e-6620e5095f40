<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_calendar" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\fragment_calendar.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_calendar_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="160" endOffset="14"/></Target><Target id="@+id/calendar_view" view="CalendarView"><Expressions/><location startLine="43" startOffset="8" endLine="48" endOffset="41"/></Target><Target id="@+id/tv_selected_date" view="TextView"><Expressions/><location startLine="68" startOffset="12" endLine="75" endOffset="45"/></Target><Target id="@+id/tv_task_count" view="TextView"><Expressions/><location startLine="77" startOffset="12" endLine="83" endOffset="45"/></Target><Target id="@+id/tv_date_number" view="TextView"><Expressions/><location startLine="87" startOffset="8" endLine="96" endOffset="59"/></Target><Target id="@+id/cb_show_incomplete_only_calendar" view="CheckBox"><Expressions/><location startLine="139" startOffset="12" endLine="146" endOffset="53"/></Target><Target id="@+id/recycler_view_day_tasks" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="150" startOffset="8" endLine="156" endOffset="42"/></Target></Targets></Layout>