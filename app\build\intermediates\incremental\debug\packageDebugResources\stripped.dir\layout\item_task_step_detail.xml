<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeColor="#E0E0E0"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Checkbox -->
        <CheckBox
            android:id="@+id/cb_step_completed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:buttonTint="@color/primary" />

        <!-- Step Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Step Title -->
            <TextView
                android:id="@+id/tv_step_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Tiêu đề bước"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:maxLines="2"
                android:ellipsize="end" />

            <!-- Step Description -->
            <TextView
                android:id="@+id/tv_step_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Mô tả bước"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="4dp"
                android:maxLines="3"
                android:ellipsize="end"
                android:visibility="gone" />

            <!-- Step Status -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_step_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Chưa hoàn thành"
                    android:textSize="10sp"
                    android:textColor="@android:color/darker_gray"
                    android:background="@drawable/bg_step_status"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- Edit Button -->
            <ImageButton
                android:id="@+id/btn_edit_step"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_edit"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginEnd="4dp"
                android:contentDescription="Chỉnh sửa bước"
                android:tint="@color/primary" />

            <!-- Delete Button -->
            <ImageButton
                android:id="@+id/btn_delete_step_detail"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_delete"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Xóa bước"
                android:tint="@color/error" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
