{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-49:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\2d8d5f7fb895a21be872deae2d339fda\\transformed\\material-1.11.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1151,1248,1328,1390,1482,1549,1623,1684,1763,1827,1881,1997,2056,2118,2172,2254,2383,2475,2559,2703,2782,2863,3010,3103,3182,3237,3288,3354,3433,3514,3605,3685,3757,3835,3910,3982,4093,4190,4267,4365,4463,4541,4622,4722,4779,4863,4929,5012,5099,5161,5225,5288,5364,5466,5573,5670,5776,5835,5890", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,146,92,78,54,50,65,78,80,90,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88", "endOffsets": "278,385,493,575,676,773,873,995,1080,1146,1243,1323,1385,1477,1544,1618,1679,1758,1822,1876,1992,2051,2113,2167,2249,2378,2470,2554,2698,2777,2858,3005,3098,3177,3232,3283,3349,3428,3509,3600,3680,3752,3830,3905,3977,4088,4185,4262,4360,4458,4536,4617,4717,4774,4858,4924,5007,5094,5156,5220,5283,5359,5461,5568,5665,5771,5830,5885,5974"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3083,3190,3298,3380,3481,3578,3678,3800,3885,3951,4048,4128,4190,4282,4349,4423,4484,4563,4627,4681,4797,4856,4918,4972,5054,5183,5275,5359,5503,5582,5663,5810,5903,5982,6037,6088,6154,6233,6314,6405,6485,6557,6635,6710,6782,6893,6990,7067,7165,7263,7341,7422,7522,7579,7663,7729,7812,7899,7961,8025,8088,8164,8266,8373,8470,8576,8635,8929", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,146,92,78,54,50,65,78,80,90,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88", "endOffsets": "328,3185,3293,3375,3476,3573,3673,3795,3880,3946,4043,4123,4185,4277,4344,4418,4479,4558,4622,4676,4792,4851,4913,4967,5049,5178,5270,5354,5498,5577,5658,5805,5898,5977,6032,6083,6149,6228,6309,6400,6480,6552,6630,6705,6777,6888,6985,7062,7160,7258,7336,7417,7517,7574,7658,7724,7807,7894,7956,8020,8083,8159,8261,8368,8465,8571,8630,8685,9013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f355e2c491578f6678abe77c9acfefb\\transformed\\core-1.9.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "9105", "endColumns": "100", "endOffsets": "9201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e863a913901ac1fc60809b473b0b8b0a\\transformed\\navigation-ui-2.7.5\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,125", "endOffsets": "163,289"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "8690,8803", "endColumns": "112,125", "endOffsets": "8798,8924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1b0065abc221b701314ae43d0b8cfc44\\transformed\\appcompat-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,9018", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,9100"}}]}]}