<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/header_gradient"
        android:orientation="vertical"
        android:padding="20dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📅 Lịch của tôi"
            android:textColor="@android:color/white"
            android:textSize="24sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="<PERSON><PERSON><PERSON>n lý thời gian hiệu quả"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:alpha="0.9" />

    </LinearLayout>

    <!-- Calendar Card - Compact -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="280dp"
        android:layout_margin="12dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <CalendarView
            android:id="@+id/calendar_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:theme="@style/CalendarViewCustom"
            android:layout_margin="8dp" />

    </com.google.android.material.card.MaterialCardView>

    <!-- Selected Date Info - Compact -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        android:background="@drawable/bg_selected_date">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_selected_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Thứ Hai, 25 Tháng 12 2024"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333333" />

            <TextView
                android:id="@+id/tv_task_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="3/5 nhiệm vụ hoàn thành"
                android:textSize="12sp"
                android:textColor="#666666" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_date_number"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:text="25"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/primary"
            android:gravity="center"
            android:background="@drawable/bg_date_circle" />

    </LinearLayout>

    <!-- Tasks for Selected Date -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingHorizontal="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="12dp">

            <!-- Header Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Nhiệm vụ trong ngày"
                    android:textColor="#333333"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_filter"
                    android:tint="#666666" />

            </LinearLayout>

            <!-- Filter Row -->
            <CheckBox
                android:id="@+id/cb_show_incomplete_only_calendar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Chưa hoàn thành"
                android:textSize="14sp"
                android:buttonTint="@color/primary" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_day_tasks"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:clipToPadding="false"
            android:paddingBottom="16dp" />

    </LinearLayout>

</LinearLayout>
