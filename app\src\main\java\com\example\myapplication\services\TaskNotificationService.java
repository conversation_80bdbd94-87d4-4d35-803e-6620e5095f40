package com.example.myapplication.services;

import android.app.Service;
import android.content.Intent;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.Nullable;

import com.example.myapplication.auth.AuthManager;
import com.example.myapplication.database.TodoDatabase;
import com.example.myapplication.database.entities.CategoryEntity;
import com.example.myapplication.database.entities.TaskEntity;
import com.example.myapplication.models.Category;
import com.example.myapplication.models.Task;
import com.example.myapplication.utils.NotificationHelper;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class TaskNotificationService extends Service {
    private static final String TAG = "TaskNotificationService";
    private static final long CHECK_INTERVAL = 60000; // Check every minute

    private Handler handler;
    private Runnable checkTasksRunnable;
    private NotificationHelper notificationHelper;
    private TodoDatabase database;
    private AuthManager authManager;
    private ExecutorService executor;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Service created");
        
        notificationHelper = new NotificationHelper(this);
        database = TodoDatabase.getDatabase(this);
        authManager = AuthManager.getInstance(this);
        executor = Executors.newSingleThreadExecutor();
        
        handler = new Handler(Looper.getMainLooper());
        
        checkTasksRunnable = new Runnable() {
            @Override
            public void run() {
                checkAndNotifyTasks();
                handler.postDelayed(this, CHECK_INTERVAL);
            }
        };
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "Service started");
        handler.post(checkTasksRunnable);
        return START_STICKY; // Restart if killed
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "Service destroyed");
        if (handler != null && checkTasksRunnable != null) {
            handler.removeCallbacks(checkTasksRunnable);
        }
        if (executor != null) {
            executor.shutdown();
        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null; // Not a bound service
    }

    private void checkAndNotifyTasks() {
        if (!authManager.isLoggedIn()) {
            return;
        }

        executor.execute(() -> {
            try {
                int currentUserId = authManager.getCurrentUserId();
                List<TaskEntity> taskEntities = database.taskDao().getTasksByUserSync(currentUserId);
                List<CategoryEntity> categoryEntities = database.categoryDao().getAllCategoriesSync();

                for (TaskEntity taskEntity : taskEntities) {
                    if (taskEntity.isCompleted() || taskEntity.getStartDate() == null) {
                        continue;
                    }

                    // Find category for this task
                    Category category = null;
                    for (CategoryEntity categoryEntity : categoryEntities) {
                        if (categoryEntity.getId() == taskEntity.getCategoryId()) {
                            category = new Category(
                                String.valueOf(categoryEntity.getId()),
                                categoryEntity.getName(),
                                categoryEntity.getColor(),
                                categoryEntity.getIcon()
                            );
                            break;
                        }
                    }

                    if (category == null) {
                        category = new Category("0", "Chung", "#2196F3", "📝");
                    }

                    Task task = taskEntity.toTask(category);

                    // Check if task should notify now
                    if (task.shouldNotify()) {
                        Log.d(TAG, "Showing notification for task: " + task.getTitle());
                        notificationHelper.showTaskNotification(
                            task.getId(),
                            task.getTitle(),
                            task.getDescription(),
                            task.getFormattedStartDate()
                        );
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error checking tasks for notifications", e);
            }
        });
    }
}
