package com.example.myapplication.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.example.myapplication.utils.NotificationHelper;

public class TaskNotificationReceiver extends BroadcastReceiver {
    private static final String TAG = "TaskNotificationReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "Received notification broadcast");

        String taskId = intent.getStringExtra("task_id");
        String taskTitle = intent.getStringExtra("task_title");
        String taskDescription = intent.getStringExtra("task_description");
        String startTime = intent.getStringExtra("start_time");

        if (taskId != null && taskTitle != null) {
            NotificationHelper notificationHelper = new NotificationHelper(context);
            notificationHelper.showTaskNotification(taskId, taskTitle, taskDescription, startTime);
            
            Log.d(TAG, "Notification shown for task: " + taskTitle);
        } else {
            Log.e(TAG, "Missing task information in intent");
        }
    }
}
