<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_category" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\item_category.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_category_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="45" endOffset="51"/></Target><Target id="@+id/tv_category_icon" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="47"/></Target><Target id="@+id/tv_category_name" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="32" endOffset="47"/></Target><Target id="@+id/tv_task_count" view="TextView"><Expressions/><location startLine="34" startOffset="8" endLine="41" endOffset="33"/></Target></Targets></Layout>