[{"merged": "com.example.myapplication.app-merged_res-51:/layout_item_task.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/item_task.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_priority_badge.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/priority_badge.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_dialog_add_category.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/dialog_add_category.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_bg_step_status.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/bg_step_status.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_button_primary.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/button_primary.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_dialog_task_detail.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/dialog_task_detail.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_category_background.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/category_background.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_bg_input_field.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/bg_input_field.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_email.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_email.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.myapplication.app-main-53:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.myapplication.app-main-53:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_item_task_step_edit.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/item_task_step_edit.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_profile.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_profile.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_add.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_add.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/menu_bottom_navigation_menu.xml.flat", "source": "com.example.myapplication.app-main-53:/menu/bottom_navigation_menu.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_help.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_help.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_activity_auth.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/activity_auth.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/menu_menu_main.xml.flat", "source": "com.example.myapplication.app-main-53:/menu/menu_main.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_circle_background.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/circle_background.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/xml_backup_rules.xml.flat", "source": "com.example.myapplication.app-main-53:/xml/backup_rules.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_bg_selected_date.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/bg_selected_date.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_fragment_calendar.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/fragment_calendar.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_arrow_right.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_arrow_right.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_fragment_login.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/fragment_login.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_dialog_add_task.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/dialog_add_task.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.myapplication.app-main-53:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.myapplication.app-main-53:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_bg_date_circle.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/bg_date_circle.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_search.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_search.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_calendar.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_calendar.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_fragment_task_list.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/fragment_task_list.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_priority_background.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/priority_background.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_delete.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_delete.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/xml_data_extraction_rules.xml.flat", "source": "com.example.myapplication.app-main-53:/xml/data_extraction_rules.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.myapplication.app-main-53:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_edit.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_edit.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_auth_background.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/auth_background.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_settings.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_settings.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_rounded_background.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/rounded_background.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.myapplication.app-main-53:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_item_task_step_detail.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/item_task_step_detail.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_button_outline.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/button_outline.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/navigation_nav_graph.xml.flat", "source": "com.example.myapplication.app-main-53:/navigation/nav_graph.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_statistics.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_statistics.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_tasks.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_tasks.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_header_gradient.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/header_gradient.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_person.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_person.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.myapplication.app-main-53:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_info.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_info.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_app_logo.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_app_logo.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_filter.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_filter.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_fragment_register.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/fragment_register.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_drag_handle.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_drag_handle.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.myapplication.app-main-53:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.myapplication.app-main-53:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_fragment_profile.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/fragment_profile.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_dialog_login.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/dialog_login.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_lock.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_lock.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_login_illustration.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_login_illustration.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_notification.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_notification.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.myapplication.app-main-53:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "com.example.myapplication.app-pngs-46:/drawable-anydpi-v24/ic_launcher_foreground.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_bg_selected_color.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/bg_selected_color.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/drawable_ic_launcher_background.xml.flat", "source": "com.example.myapplication.app-main-53:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/color_bottom_nav_color.xml.flat", "source": "com.example.myapplication.app-main-53:/color/bottom_nav_color.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.myapplication.app-main-53:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.myapplication.app-merged_res-51:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.myapplication.app-main-53:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_item_category.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/item_category.xml"}, {"merged": "com.example.myapplication.app-merged_res-51:/layout_activity_main.xml.flat", "source": "com.example.myapplication.app-main-53:/layout/activity_main.xml"}]