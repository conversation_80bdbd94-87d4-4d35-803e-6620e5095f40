<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_register" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\fragment_register.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_register_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="149" endOffset="12"/></Target><Target id="@+id/et_full_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="22" startOffset="12" endLine="28" endOffset="49"/></Target><Target id="@+id/et_username" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="42" startOffset="12" endLine="48" endOffset="49"/></Target><Target id="@+id/et_email" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="62" startOffset="12" endLine="68" endOffset="49"/></Target><Target id="@+id/et_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="83" startOffset="12" endLine="89" endOffset="49"/></Target><Target id="@+id/et_confirm_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="104" startOffset="12" endLine="110" endOffset="49"/></Target><Target id="@+id/cb_terms" view="CheckBox"><Expressions/><location startLine="115" startOffset="8" endLine="122" endOffset="49"/></Target><Target id="@+id/btn_register" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="125" startOffset="8" endLine="135" endOffset="48"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="138" startOffset="8" endLine="145" endOffset="56"/></Target></Targets></Layout>