<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/header_gradient"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="<PERSON><PERSON><PERSON>n lý nhiệm vụ của bạn"
                android:textColor="@android:color/white"
                android:textSize="24sp"
                android:textStyle="bold" />

            <!-- Search Bar -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="25dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginStart="8dp"
                        android:src="@drawable/ic_search"
                        android:tint="#757575" />

                    <EditText
                        android:id="@+id/et_search"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="Tìm kiếm nhiệm vụ..."
                        android:textSize="16sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- Categories -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">



            <!-- Category Filter -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Danh mục:"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:layout_marginEnd="8dp" />

                <Spinner
                    android:id="@+id/spinner_category_filter"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <Button
                    android:id="@+id/btn_add_category"
                    android:layout_width="wrap_content"
                    android:layout_height="36dp"
                    android:text="+ Danh mục"
                    android:textSize="14sp"
                    android:layout_marginStart="8dp"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </LinearLayout>

            <!-- Completion Filter -->


        </LinearLayout>

        <!-- Tasks Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingHorizontal="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Nhiệm vụ"
                    android:textColor="#333333"
                    android:textSize="20sp"
                    android:textStyle="bold" />
                <CheckBox
                    android:id="@+id/cb_show_incomplete_only"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="16dp"
                    android:text="Chưa hoàn thành"
                    android:textSize="14sp"
                    android:buttonTint="@color/primary" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_tasks"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="12dp"
                android:layout_weight="1"
                android:clipToPadding="false"
                android:paddingBottom="80dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_task"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        app:backgroundTint="#2196F3"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
