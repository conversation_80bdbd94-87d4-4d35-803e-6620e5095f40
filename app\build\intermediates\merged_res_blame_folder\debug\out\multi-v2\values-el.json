{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-49:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f355e2c491578f6678abe77c9acfefb\\transformed\\core-1.9.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "9096", "endColumns": "100", "endOffsets": "9192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1b0065abc221b701314ae43d0b8cfc44\\transformed\\appcompat-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,438,549,666,751,857,980,1069,1154,1245,1338,1433,1527,1627,1720,1815,1912,2003,2094,2179,2290,2399,2501,2612,2722,2830,3001,9010", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "433,544,661,746,852,975,1064,1149,1240,1333,1428,1522,1622,1715,1810,1907,1998,2089,2174,2285,2394,2496,2607,2717,2825,2996,3096,9091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\2d8d5f7fb895a21be872deae2d339fda\\transformed\\material-1.11.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,358,444,529,625,712,814,931,1017,1083,1183,1265,1328,1419,1482,1547,1609,1678,1740,1794,1932,1989,2050,2104,2177,2330,2415,2499,2638,2719,2804,2945,3035,3121,3176,3227,3293,3371,3456,3541,3624,3696,3776,3856,3927,4034,4126,4198,4295,4392,4466,4540,4642,4698,4785,4857,4945,5037,5099,5163,5226,5296,5412,5521,5630,5735,5794,5849", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,87,85,84,95,86,101,116,85,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,83,138,80,84,140,89,85,54,50,65,77,84,84,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90", "endOffsets": "265,353,439,524,620,707,809,926,1012,1078,1178,1260,1323,1414,1477,1542,1604,1673,1735,1789,1927,1984,2045,2099,2172,2325,2410,2494,2633,2714,2799,2940,3030,3116,3171,3222,3288,3366,3451,3536,3619,3691,3771,3851,3922,4029,4121,4193,4290,4387,4461,4535,4637,4693,4780,4852,4940,5032,5094,5158,5221,5291,5407,5516,5625,5730,5789,5844,5935"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3101,3189,3275,3360,3456,3543,3645,3762,3848,3914,4014,4096,4159,4250,4313,4378,4440,4509,4571,4625,4763,4820,4881,4935,5008,5161,5246,5330,5469,5550,5635,5776,5866,5952,6007,6058,6124,6202,6287,6372,6455,6527,6607,6687,6758,6865,6957,7029,7126,7223,7297,7371,7473,7529,7616,7688,7776,7868,7930,7994,8057,8127,8243,8352,8461,8566,8625,8919", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,102", "endColumns": "12,87,85,84,95,86,101,116,85,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,83,138,80,84,140,89,85,54,50,65,77,84,84,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90", "endOffsets": "315,3184,3270,3355,3451,3538,3640,3757,3843,3909,4009,4091,4154,4245,4308,4373,4435,4504,4566,4620,4758,4815,4876,4930,5003,5156,5241,5325,5464,5545,5630,5771,5861,5947,6002,6053,6119,6197,6282,6367,6450,6522,6602,6682,6753,6860,6952,7024,7121,7218,7292,7366,7468,7524,7611,7683,7771,7863,7925,7989,8052,8122,8238,8347,8456,8561,8620,8675,9005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e863a913901ac1fc60809b473b0b8b0a\\transformed\\navigation-ui-2.7.5\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,121", "endOffsets": "167,289"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "8680,8797", "endColumns": "116,121", "endOffsets": "8792,8914"}}]}]}