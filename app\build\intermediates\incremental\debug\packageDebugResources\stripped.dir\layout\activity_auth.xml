<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/auth_background"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:minHeight="@dimen/match_parent">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:paddingHorizontal="32dp"
            android:paddingTop="48dp"
            android:paddingBottom="24dp">

            <!-- Logo/Icon -->
            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_app_logo"
                android:layout_marginBottom="16dp"
                android:contentDescription="App Logo" />

            <!-- App Name -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="TodoMaster"
                android:textSize="28sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:layout_marginBottom="4dp" />

            <!-- Subtitle -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Quản lý nhiệm vụ thông minh"
                android:textSize="14sp"
                android:textColor="@android:color/white"
                android:alpha="0.9" />

        </LinearLayout>

        <!-- Auth Form Container -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- Tab Layout -->
                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tab_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    app:tabIndicatorColor="@color/primary"
                    app:tabSelectedTextColor="@color/primary"
                    app:tabTextColor="#666666">

                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Đăng nhập" />

                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Đăng ký" />

                </com.google.android.material.tabs.TabLayout>

                <!-- ViewPager for Login/Register Forms -->
                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/view_pager"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Footer -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="© 2024 TodoMaster"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                android:alpha="0.7" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
